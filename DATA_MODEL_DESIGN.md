# 数据模型设计详细方案

## 1. PostgreSQL 数据模型 (主数据库)

### 1.1 用户管理模块

```sql
-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    display_name VARCHAR(100),
    avatar_url VARCHAR(500),
    email_verified BOOLEAN DEFAULT FALSE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'deleted')),
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE
);

-- 用户会话表 (可选，如果不使用Redis)
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    device_info JSONB,
    ip_address INET,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_sessions_expires ON user_sessions(expires_at);
```

### 1.2 笔记管理模块

```sql
-- 笔记表 (元数据)
CREATE TABLE notes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    content_id VARCHAR(255), -- MongoDB文档ID
    apple_notes_id VARCHAR(255), -- Apple Notes原始ID
    apple_notes_uuid VARCHAR(255), -- Apple Notes UUID
    
    -- 状态和可见性
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    is_public BOOLEAN DEFAULT FALSE,
    
    -- 分享设置
    share_token VARCHAR(255) UNIQUE,
    share_type VARCHAR(20) DEFAULT 'private' CHECK (share_type IN ('private', 'public', 'password', 'link')),
    share_password_hash VARCHAR(255),
    share_expires_at TIMESTAMP WITH TIME ZONE,
    max_views INTEGER,
    current_views INTEGER DEFAULT 0,
    
    -- 元数据
    word_count INTEGER DEFAULT 0,
    has_attachments BOOLEAN DEFAULT FALSE,
    tags TEXT[], -- PostgreSQL数组类型
    apple_metadata JSONB, -- Apple Notes原始元数据
    
    -- 时间戳
    apple_created_at TIMESTAMP WITH TIME ZONE,
    apple_modified_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    published_at TIMESTAMP WITH TIME ZONE,
    last_synced_at TIMESTAMP WITH TIME ZONE
);

-- 笔记版本历史表
CREATE TABLE note_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    note_id UUID REFERENCES notes(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    content_id VARCHAR(255), -- MongoDB文档ID
    title VARCHAR(500),
    change_summary TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(note_id, version_number)
);

-- 笔记标签表 (如果需要标签管理)
CREATE TABLE tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    color VARCHAR(7), -- HEX颜色值
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, name)
);

-- 笔记标签关联表
CREATE TABLE note_tags (
    note_id UUID REFERENCES notes(id) ON DELETE CASCADE,
    tag_id UUID REFERENCES tags(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    PRIMARY KEY (note_id, tag_id)
);

-- 索引
CREATE INDEX idx_notes_user_id ON notes(user_id);
CREATE INDEX idx_notes_share_token ON notes(share_token) WHERE share_token IS NOT NULL;
CREATE INDEX idx_notes_status ON notes(status);
CREATE INDEX idx_notes_is_public ON notes(is_public) WHERE is_public = TRUE;
CREATE INDEX idx_notes_apple_id ON notes(apple_notes_id) WHERE apple_notes_id IS NOT NULL;
CREATE INDEX idx_notes_tags ON notes USING GIN(tags);
CREATE INDEX idx_notes_created_at ON notes(created_at);
CREATE INDEX idx_note_versions_note_id ON note_versions(note_id);
```

### 1.3 访问控制和分享模块

```sql
-- 分享访问日志表
CREATE TABLE share_access_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    note_id UUID REFERENCES notes(id) ON DELETE CASCADE,
    share_token VARCHAR(255),
    visitor_ip INET,
    user_agent TEXT,
    referer TEXT,
    access_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 地理位置信息 (可选)
    country VARCHAR(2),
    city VARCHAR(100)
);

-- 协作权限表 (未来扩展)
CREATE TABLE note_collaborators (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    note_id UUID REFERENCES notes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    permission VARCHAR(20) DEFAULT 'read' CHECK (permission IN ('read', 'comment', 'edit')),
    invited_by UUID REFERENCES users(id),
    invited_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    accepted_at TIMESTAMP WITH TIME ZONE,
    
    UNIQUE(note_id, user_id)
);

-- 索引
CREATE INDEX idx_access_logs_note_id ON share_access_logs(note_id);
CREATE INDEX idx_access_logs_time ON share_access_logs(access_time);
CREATE INDEX idx_collaborators_note_id ON note_collaborators(note_id);
CREATE INDEX idx_collaborators_user_id ON note_collaborators(user_id);
```

### 1.4 系统配置和统计

```sql
-- 系统配置表
CREATE TABLE system_settings (
    key VARCHAR(100) PRIMARY KEY,
    value JSONB NOT NULL,
    description TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用户统计表
CREATE TABLE user_statistics (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    total_notes INTEGER DEFAULT 0,
    public_notes INTEGER DEFAULT 0,
    total_views INTEGER DEFAULT 0,
    total_shares INTEGER DEFAULT 0,
    storage_used BIGINT DEFAULT 0, -- 字节数
    last_calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 系统统计表 (每日汇总)
CREATE TABLE daily_statistics (
    date DATE PRIMARY KEY,
    new_users INTEGER DEFAULT 0,
    new_notes INTEGER DEFAULT 0,
    total_views INTEGER DEFAULT 0,
    active_users INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 2. MongoDB 数据模型 (内容存储)

### 2.1 笔记内容文档

```javascript
// Collection: note_contents
{
  _id: ObjectId("..."),
  noteId: "uuid-from-postgresql", // 关联PostgreSQL的notes表
  version: 1, // 版本号
  
  // 原始内容
  rawContent: {
    type: "apple_notes_rtf", // 或 "html", "markdown"
    data: "...", // 原始数据
    encoding: "utf-8"
  },
  
  // 处理后的内容
  processedContent: {
    html: "...", // 转换后的HTML
    plainText: "...", // 纯文本版本
    markdown: "...", // Markdown版本 (可选)
    wordCount: 1234,
    readingTime: 5 // 预估阅读时间(分钟)
  },
  
  // 附件信息
  attachments: [
    {
      id: "attachment-uuid",
      type: "image", // image, file, audio, video
      originalName: "photo.jpg",
      mimeType: "image/jpeg",
      size: 1024000, // 字节数
      
      // 存储信息
      storage: {
        provider: "s3", // s3, local, cdn
        bucket: "applenotes-attachments",
        key: "users/user-id/notes/note-id/attachments/file-hash.jpg",
        url: "https://cdn.example.com/...",
        thumbnailUrl: "https://cdn.example.com/..." // 缩略图
      },
      
      // 元数据
      metadata: {
        width: 1920, // 图片宽度
        height: 1080, // 图片高度
        duration: 120, // 视频/音频时长(秒)
        exif: {...} // EXIF信息
      },
      
      createdAt: ISODate("..."),
      updatedAt: ISODate("...")
    }
  ],
  
  // Apple Notes特定数据
  appleNotesData: {
    originalFormat: "rtf",
    tables: [...], // 表格数据
    drawings: [...], // 手绘内容
    checklists: [...], // 清单项目
    links: [...] // 链接信息
  },
  
  // 搜索和索引
  searchableText: "...", // 用于全文搜索的文本
  keywords: ["keyword1", "keyword2"], // 提取的关键词
  
  // 时间戳
  createdAt: ISODate("..."),
  updatedAt: ISODate("..."),
  lastProcessedAt: ISODate("...")
}
```

### 2.2 附件详细信息文档

```javascript
// Collection: attachments
{
  _id: ObjectId("..."),
  attachmentId: "uuid", // 唯一标识符
  noteId: "note-uuid",
  userId: "user-uuid",
  
  // 文件信息
  file: {
    originalName: "document.pdf",
    sanitizedName: "document_20231201_abc123.pdf",
    mimeType: "application/pdf",
    size: 2048000,
    hash: "sha256-hash", // 文件哈希，用于去重
    extension: "pdf"
  },
  
  // 存储信息
  storage: {
    provider: "s3",
    region: "us-west-2",
    bucket: "applenotes-files",
    key: "attachments/2023/12/01/sha256-hash.pdf",
    publicUrl: "https://cdn.example.com/...",
    privateUrl: "https://s3.amazonaws.com/...",
    
    // 多版本存储 (不同尺寸的图片等)
    variants: [
      {
        type: "thumbnail",
        size: "150x150",
        url: "https://cdn.example.com/thumb_150x150.jpg"
      },
      {
        type: "medium",
        size: "800x600",
        url: "https://cdn.example.com/medium_800x600.jpg"
      }
    ]
  },
  
  // 处理状态
  processing: {
    status: "completed", // pending, processing, completed, failed
    thumbnailGenerated: true,
    textExtracted: true,
    virusScanned: true,
    lastProcessedAt: ISODate("...")
  },
  
  // 访问控制
  access: {
    isPublic: false,
    allowDirectAccess: true,
    downloadCount: 0,
    lastAccessedAt: ISODate("...")
  },
  
  createdAt: ISODate("..."),
  updatedAt: ISODate("...")
}
```

### 2.3 搜索索引文档

```javascript
// Collection: search_index
{
  _id: ObjectId("..."),
  noteId: "note-uuid",
  userId: "user-uuid",
  
  // 搜索内容
  content: {
    title: "笔记标题",
    text: "笔记的完整文本内容...",
    tags: ["标签1", "标签2"],
    attachmentNames: ["文件1.pdf", "图片1.jpg"]
  },
  
  // 搜索权重
  weights: {
    title: 10,
    text: 1,
    tags: 5,
    attachments: 2
  },
  
  // 元数据
  metadata: {
    wordCount: 500,
    language: "zh-CN",
    lastIndexedAt: ISODate("...")
  },
  
  createdAt: ISODate("..."),
  updatedAt: ISODate("...")
}
```

## 3. Redis 数据结构 (缓存层)

### 3.1 缓存键命名规范

```
用户会话: session:{user_id}:{session_id}
用户信息: user:{user_id}
笔记缓存: note:{note_id}
分享链接: share:{share_token}
热门笔记: trending:notes
搜索结果: search:{query_hash}
```

### 3.2 具体数据结构

```redis
# 用户会话 (Hash)
HSET session:user123:sess456
  user_id "user123"
  email "<EMAIL>"
  display_name "用户名"
  permissions "read,write"
  created_at "2023-12-01T10:00:00Z"
  expires_at "2023-12-02T10:00:00Z"

# 笔记缓存 (Hash)
HSET note:note456
  title "我的笔记"
  content_preview "这是笔记的前100个字符..."
  is_public "true"
  view_count "123"
  cached_at "2023-12-01T10:00:00Z"

# 分享链接 (Hash)
HSET share:abc123token
  note_id "note456"
  user_id "user123"
  share_type "public"
  expires_at "2023-12-31T23:59:59Z"
  max_views "1000"
  current_views "45"

# 热门笔记排行 (Sorted Set)
ZADD trending:notes
  150 "note456"  # score为浏览量
  89 "note789"
  67 "note012"

# 用户笔记列表 (List)
LPUSH user:user123:notes "note456" "note789" "note012"

# 搜索结果缓存 (String - JSON)
SET search:hash123 '{"results":[{"id":"note456","title":"..."}],"total":10}'
EXPIRE search:hash123 300  # 5分钟过期
```

## 4. 数据关系图

```mermaid
erDiagram
    USERS ||--o{ NOTES : owns
    USERS ||--o{ USER_SESSIONS : has
    USERS ||--o{ TAGS : creates
    NOTES ||--o{ NOTE_VERSIONS : has
    NOTES ||--o{ SHARE_ACCESS_LOGS : generates
    NOTES ||--o{ NOTE_TAGS : tagged_with
    TAGS ||--o{ NOTE_TAGS : applied_to
    NOTES ||--o{ NOTE_COLLABORATORS : shared_with
    USERS ||--o{ NOTE_COLLABORATORS : collaborates
    
    USERS {
        uuid id PK
        string email UK
        string password_hash
        string display_name
        timestamp created_at
    }
    
    NOTES {
        uuid id PK
        uuid user_id FK
        string title
        string content_id
        boolean is_public
        string share_token UK
        timestamp created_at
    }
```

这个数据模型设计充分考虑了Apple Notes分享系统的需求，既保证了数据的一致性和安全性，又具备良好的扩展性和性能。
