# 安全方案设计

## 1. 认证与授权架构

### 1.1 JWT Token 认证机制

```typescript
// JWT Payload 结构
interface JWTPayload {
  sub: string;        // 用户ID
  email: string;      // 用户邮箱
  role: string;       // 用户角色
  permissions: string[]; // 权限列表
  iat: number;        // 签发时间
  exp: number;        // 过期时间
  jti: string;        // Token唯一标识
}

// Token 配置
const JWT_CONFIG = {
  accessToken: {
    secret: process.env.JWT_ACCESS_SECRET,
    expiresIn: '15m',        // 15分钟
    algorithm: 'HS256'
  },
  refreshToken: {
    secret: process.env.JWT_REFRESH_SECRET,
    expiresIn: '7d',         // 7天
    algorithm: 'HS256'
  }
};
```

### 1.2 多层认证策略

```typescript
// 认证中间件
class AuthMiddleware {
  // 基础Token验证
  async verifyToken(token: string): Promise<JWTPayload> {
    try {
      const payload = jwt.verify(token, JWT_CONFIG.accessToken.secret);
      
      // 检查Token是否在黑名单中
      const isBlacklisted = await redis.get(`blacklist:${payload.jti}`);
      if (isBlacklisted) {
        throw new Error('Token已被撤销');
      }
      
      return payload as JWTPayload;
    } catch (error) {
      throw new UnauthorizedError('无效的访问令牌');
    }
  }
  
  // 权限验证
  async checkPermission(userId: string, resource: string, action: string): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId);
    return userPermissions.includes(`${resource}:${action}`) || 
           userPermissions.includes(`${resource}:*`) ||
           userPermissions.includes('*:*');
  }
}
```

### 1.3 基于角色的访问控制 (RBAC)

```sql
-- 角色表
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用户角色关联表
CREATE TABLE user_roles (
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    assigned_by UUID REFERENCES users(id),
    
    PRIMARY KEY (user_id, role_id)
);

-- 权限定义
INSERT INTO roles (name, description, permissions) VALUES
('user', '普通用户', '["notes:read", "notes:create", "notes:update", "notes:delete", "share:create"]'),
('premium', '高级用户', '["notes:*", "share:*", "analytics:read", "export:create"]'),
('admin', '管理员', '["*:*"]');
```

## 2. 数据加密与保护

### 2.1 传输层安全

```nginx
# Nginx SSL 配置
server {
    listen 443 ssl http2;
    server_name api.applenotes-share.com;
    
    # SSL 证书配置
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/private.key;
    
    # 安全头设置
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
}
```

### 2.2 数据库加密

```typescript
// 敏感数据加密工具
class EncryptionService {
  private readonly algorithm = 'aes-256-gcm';
  private readonly keyDerivation = 'pbkdf2';
  
  // 加密敏感字段
  async encryptSensitiveData(data: string): Promise<EncryptedData> {
    const salt = crypto.randomBytes(16);
    const iv = crypto.randomBytes(16);
    const key = crypto.pbkdf2Sync(process.env.ENCRYPTION_KEY, salt, 100000, 32, 'sha512');
    
    const cipher = crypto.createCipher(this.algorithm, key, iv);
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return {
      encrypted,
      salt: salt.toString('hex'),
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex')
    };
  }
  
  // 解密敏感字段
  async decryptSensitiveData(encryptedData: EncryptedData): Promise<string> {
    const key = crypto.pbkdf2Sync(
      process.env.ENCRYPTION_KEY, 
      Buffer.from(encryptedData.salt, 'hex'), 
      100000, 
      32, 
      'sha512'
    );
    
    const decipher = crypto.createDecipher(
      this.algorithm, 
      key, 
      Buffer.from(encryptedData.iv, 'hex')
    );
    
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
    
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}
```

### 2.3 密码安全策略

```typescript
class PasswordService {
  private readonly saltRounds = 12;
  private readonly minLength = 8;
  private readonly maxLength = 128;
  
  // 密码强度验证
  validatePassword(password: string): ValidationResult {
    const errors: string[] = [];
    
    if (password.length < this.minLength) {
      errors.push(`密码长度至少${this.minLength}位`);
    }
    
    if (password.length > this.maxLength) {
      errors.push(`密码长度不能超过${this.maxLength}位`);
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('密码必须包含大写字母');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('密码必须包含小写字母');
    }
    
    if (!/\d/.test(password)) {
      errors.push('密码必须包含数字');
    }
    
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('密码必须包含特殊字符');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  // 密码哈希
  async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, this.saltRounds);
  }
  
  // 密码验证
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }
}
```

## 3. 访问控制与权限管理

### 3.1 笔记访问控制

```typescript
class NoteAccessControl {
  // 检查笔记访问权限
  async checkNoteAccess(userId: string, noteId: string, action: string): Promise<boolean> {
    const note = await this.noteRepository.findById(noteId);
    if (!note) return false;
    
    // 笔记所有者拥有所有权限
    if (note.userId === userId) return true;
    
    // 公开笔记的读取权限
    if (action === 'read' && note.isPublic) return true;
    
    // 检查协作权限
    const collaboration = await this.collaborationRepository.findByNoteAndUser(noteId, userId);
    if (collaboration) {
      return this.checkCollaborationPermission(collaboration.permission, action);
    }
    
    return false;
  }
  
  // 分享链接访问控制
  async validateShareAccess(shareToken: string, password?: string): Promise<ShareValidationResult> {
    const shareSettings = await this.shareRepository.findByToken(shareToken);
    if (!shareSettings) {
      throw new NotFoundError('分享链接不存在');
    }
    
    // 检查过期时间
    if (shareSettings.expiresAt && new Date() > shareSettings.expiresAt) {
      throw new ForbiddenError('分享链接已过期');
    }
    
    // 检查访问次数限制
    if (shareSettings.maxViews && shareSettings.currentViews >= shareSettings.maxViews) {
      throw new ForbiddenError('分享链接访问次数已达上限');
    }
    
    // 检查密码保护
    if (shareSettings.shareType === 'password') {
      if (!password) {
        throw new UnauthorizedError('需要密码访问');
      }
      
      const isValidPassword = await bcrypt.compare(password, shareSettings.passwordHash);
      if (!isValidPassword) {
        throw new UnauthorizedError('密码错误');
      }
    }
    
    return {
      isValid: true,
      noteId: shareSettings.noteId,
      allowDownload: shareSettings.allowDownload
    };
  }
}
```

### 3.2 API 速率限制

```typescript
// Redis 基础的速率限制
class RateLimiter {
  async checkRateLimit(key: string, limit: number, window: number): Promise<RateLimitResult> {
    const current = await redis.incr(key);
    
    if (current === 1) {
      await redis.expire(key, window);
    }
    
    const ttl = await redis.ttl(key);
    
    return {
      allowed: current <= limit,
      remaining: Math.max(0, limit - current),
      resetTime: Date.now() + (ttl * 1000),
      total: limit
    };
  }
}

// 速率限制中间件
const rateLimitMiddleware = {
  // 通用API限制: 每分钟100次
  general: rateLimit({
    windowMs: 60 * 1000,
    max: 100,
    message: '请求过于频繁，请稍后再试'
  }),
  
  // 登录限制: 每15分钟5次
  auth: rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 5,
    skipSuccessfulRequests: true,
    message: '登录尝试过于频繁，请15分钟后再试'
  }),
  
  // 文件上传限制: 每小时20次
  upload: rateLimit({
    windowMs: 60 * 60 * 1000,
    max: 20,
    message: '文件上传过于频繁，请稍后再试'
  })
};
```

## 4. 输入验证与防护

### 4.1 输入验证框架

```typescript
// 使用 Zod 进行输入验证
const noteCreateSchema = z.object({
  title: z.string()
    .min(1, '标题不能为空')
    .max(500, '标题长度不能超过500字符')
    .regex(/^[^<>]*$/, '标题不能包含HTML标签'),
    
  content: z.object({
    type: z.enum(['html', 'markdown', 'rtf']),
    data: z.string()
      .max(1000000, '内容长度不能超过1MB')
  }),
  
  tags: z.array(z.string().max(50)).max(10, '标签数量不能超过10个'),
  
  isPublic: z.boolean().default(false),
  
  appleNotesData: z.object({
    appleNotesId: z.string().optional(),
    appleNotesUuid: z.string().uuid().optional(),
    originalFormat: z.string().optional()
  }).optional()
});

// 验证中间件
const validateInput = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      req.body = schema.parse(req.body);
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '输入数据验证失败',
            details: error.errors
          }
        });
      }
      next(error);
    }
  };
};
```

### 4.2 XSS 防护

```typescript
// HTML 内容清理
import DOMPurify from 'isomorphic-dompurify';

class ContentSanitizer {
  // 清理HTML内容
  sanitizeHTML(html: string): string {
    return DOMPurify.sanitize(html, {
      ALLOWED_TAGS: [
        'p', 'br', 'strong', 'em', 'u', 's', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'ul', 'ol', 'li', 'blockquote', 'a', 'img', 'table', 'thead', 'tbody', 
        'tr', 'td', 'th', 'code', 'pre'
      ],
      ALLOWED_ATTR: ['href', 'src', 'alt', 'title', 'class'],
      ALLOWED_URI_REGEXP: /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i
    });
  }
  
  // 清理用户输入
  sanitizeUserInput(input: string): string {
    return input
      .replace(/[<>]/g, '') // 移除尖括号
      .replace(/javascript:/gi, '') // 移除javascript协议
      .trim();
  }
}
```

### 4.3 SQL 注入防护

```typescript
// 使用参数化查询
class NoteRepository {
  async findByUserId(userId: string, filters: NoteFilters): Promise<Note[]> {
    const query = `
      SELECT * FROM notes 
      WHERE user_id = $1 
      AND ($2::text IS NULL OR status = $2)
      AND ($3::text IS NULL OR title ILIKE $3)
      ORDER BY created_at DESC
      LIMIT $4 OFFSET $5
    `;
    
    const params = [
      userId,
      filters.status || null,
      filters.search ? `%${filters.search}%` : null,
      filters.limit || 20,
      (filters.page - 1) * (filters.limit || 20)
    ];
    
    const result = await this.db.query(query, params);
    return result.rows;
  }
}
```

## 5. 审计与监控

### 5.1 安全事件日志

```typescript
// 安全事件记录
class SecurityAuditLogger {
  async logSecurityEvent(event: SecurityEvent): Promise<void> {
    const logEntry = {
      timestamp: new Date().toISOString(),
      eventType: event.type,
      userId: event.userId,
      ip: event.ip,
      userAgent: event.userAgent,
      resource: event.resource,
      action: event.action,
      result: event.result,
      details: event.details,
      severity: event.severity
    };
    
    // 记录到数据库
    await this.auditRepository.create(logEntry);
    
    // 高危事件实时告警
    if (event.severity === 'HIGH' || event.severity === 'CRITICAL') {
      await this.alertService.sendSecurityAlert(logEntry);
    }
  }
}

// 安全事件类型
enum SecurityEventType {
  LOGIN_SUCCESS = 'login_success',
  LOGIN_FAILED = 'login_failed',
  PASSWORD_CHANGED = 'password_changed',
  UNAUTHORIZED_ACCESS = 'unauthorized_access',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  DATA_EXPORT = 'data_export',
  ADMIN_ACTION = 'admin_action'
}
```

### 5.2 异常检测

```typescript
// 异常行为检测
class AnomalyDetector {
  // 检测异常登录
  async detectAnomalousLogin(userId: string, loginData: LoginData): Promise<boolean> {
    const recentLogins = await this.getRecentLogins(userId, 30); // 30天内登录记录
    
    // 检查地理位置异常
    const isLocationAnomaly = this.checkLocationAnomaly(loginData.ip, recentLogins);
    
    // 检查时间异常
    const isTimeAnomaly = this.checkTimeAnomaly(loginData.timestamp, recentLogins);
    
    // 检查设备异常
    const isDeviceAnomaly = this.checkDeviceAnomaly(loginData.userAgent, recentLogins);
    
    return isLocationAnomaly || isTimeAnomaly || isDeviceAnomaly;
  }
  
  // 检测批量操作
  async detectBulkOperations(userId: string, timeWindow: number = 300): Promise<boolean> {
    const recentActions = await this.getRecentActions(userId, timeWindow);
    
    // 检查是否在短时间内进行了大量操作
    const actionCounts = this.countActionsByType(recentActions);
    
    return actionCounts.create > 50 || // 5分钟内创建超过50个笔记
           actionCounts.delete > 20 || // 5分钟内删除超过20个笔记
           actionCounts.share > 30;    // 5分钟内分享超过30次
  }
}
```

这个安全方案涵盖了认证授权、数据保护、访问控制、输入验证和安全监控等关键方面，为Apple Notes分享系统提供了全面的安全保障。
