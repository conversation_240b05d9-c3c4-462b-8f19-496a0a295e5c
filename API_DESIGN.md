# RESTful API 接口设计

## 1. API 基础规范

### 1.1 基础信息
- **Base URL**: `https://api.applenotes-share.com/v1`
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8
- **HTTP状态码**: 标准RESTful状态码

### 1.2 通用响应格式

```typescript
// 成功响应
interface SuccessResponse<T> {
  success: true;
  data: T;
  message?: string;
  meta?: {
    pagination?: PaginationMeta;
    timestamp: string;
  };
}

// 错误响应
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  meta: {
    timestamp: string;
    requestId: string;
  };
}

// 分页元数据
interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}
```

### 1.3 通用请求头

```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
Accept: application/json
X-Client-Version: 1.0.0
X-Platform: web|macos
```

## 2. 认证相关 API

### 2.1 用户注册
```http
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "displayName": "用户名",
  "acceptTerms": true
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "displayName": "用户名",
      "emailVerified": false,
      "createdAt": "2023-12-01T10:00:00Z"
    },
    "tokens": {
      "accessToken": "jwt_access_token",
      "refreshToken": "jwt_refresh_token",
      "expiresIn": 3600
    }
  }
}
```

### 2.2 用户登录
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "rememberMe": true
}
```

### 2.3 刷新Token
```http
POST /auth/refresh
Content-Type: application/json

{
  "refreshToken": "jwt_refresh_token"
}
```

### 2.4 用户登出
```http
POST /auth/logout
Authorization: Bearer <access_token>

{
  "allDevices": false
}
```

### 2.5 获取当前用户信息
```http
GET /auth/me
Authorization: Bearer <access_token>
```

## 3. 笔记管理 API

### 3.1 获取笔记列表
```http
GET /notes?page=1&limit=20&status=published&search=关键词&tags=tag1,tag2&sort=created_at&order=desc
Authorization: Bearer <access_token>
```

**响应:**
```json
{
  "success": true,
  "data": {
    "notes": [
      {
        "id": "note-uuid",
        "title": "我的笔记",
        "contentPreview": "笔记内容预览...",
        "status": "published",
        "isPublic": true,
        "shareToken": "abc123",
        "tags": ["工作", "学习"],
        "wordCount": 500,
        "hasAttachments": true,
        "viewCount": 123,
        "createdAt": "2023-12-01T10:00:00Z",
        "updatedAt": "2023-12-01T15:30:00Z",
        "publishedAt": "2023-12-01T12:00:00Z"
      }
    ]
  },
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### 3.2 创建笔记
```http
POST /notes
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "title": "新笔记标题",
  "content": {
    "type": "html",
    "data": "<p>笔记内容</p>"
  },
  "tags": ["标签1", "标签2"],
  "isPublic": false,
  "appleNotesData": {
    "appleNotesId": "apple-id",
    "appleNotesUuid": "apple-uuid",
    "originalFormat": "rtf"
  }
}
```

### 3.3 获取单个笔记
```http
GET /notes/{noteId}
Authorization: Bearer <access_token>
```

**响应:**
```json
{
  "success": true,
  "data": {
    "note": {
      "id": "note-uuid",
      "title": "笔记标题",
      "content": {
        "html": "<p>完整的HTML内容</p>",
        "plainText": "纯文本内容",
        "wordCount": 500
      },
      "attachments": [
        {
          "id": "attachment-uuid",
          "type": "image",
          "originalName": "photo.jpg",
          "url": "https://cdn.example.com/photo.jpg",
          "thumbnailUrl": "https://cdn.example.com/thumb_photo.jpg",
          "size": 1024000,
          "mimeType": "image/jpeg"
        }
      ],
      "tags": ["标签1"],
      "status": "published",
      "isPublic": true,
      "shareSettings": {
        "shareType": "public",
        "shareToken": "abc123",
        "expiresAt": null,
        "maxViews": null,
        "currentViews": 123
      },
      "createdAt": "2023-12-01T10:00:00Z",
      "updatedAt": "2023-12-01T15:30:00Z"
    }
  }
}
```

### 3.4 更新笔记
```http
PUT /notes/{noteId}
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "title": "更新后的标题",
  "content": {
    "type": "html",
    "data": "<p>更新后的内容</p>"
  },
  "tags": ["新标签"],
  "isPublic": true
}
```

### 3.5 删除笔记
```http
DELETE /notes/{noteId}
Authorization: Bearer <access_token>
```

## 4. 分享功能 API

### 4.1 创建分享链接
```http
POST /notes/{noteId}/share
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "shareType": "public", // public, password, link
  "password": "optional_password",
  "expiresAt": "2024-01-01T00:00:00Z",
  "maxViews": 1000,
  "allowDownload": true
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "shareUrl": "https://share.applenotes.com/abc123token",
    "shareToken": "abc123token",
    "shareSettings": {
      "shareType": "public",
      "expiresAt": "2024-01-01T00:00:00Z",
      "maxViews": 1000,
      "currentViews": 0,
      "allowDownload": true,
      "createdAt": "2023-12-01T10:00:00Z"
    }
  }
}
```

### 4.2 通过分享链接访问笔记
```http
GET /share/{shareToken}?password=optional_password
```

**响应:**
```json
{
  "success": true,
  "data": {
    "note": {
      "title": "分享的笔记",
      "content": {
        "html": "<p>笔记内容</p>",
        "plainText": "纯文本内容"
      },
      "attachments": [...],
      "author": {
        "displayName": "作者名",
        "avatarUrl": "https://cdn.example.com/avatar.jpg"
      },
      "publishedAt": "2023-12-01T12:00:00Z",
      "viewCount": 124
    },
    "shareInfo": {
      "allowDownload": true,
      "expiresAt": "2024-01-01T00:00:00Z",
      "remainingViews": 876
    }
  }
}
```

### 4.3 更新分享设置
```http
PUT /notes/{noteId}/share
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "shareType": "password",
  "password": "new_password",
  "expiresAt": "2024-06-01T00:00:00Z",
  "maxViews": 2000
}
```

### 4.4 取消分享
```http
DELETE /notes/{noteId}/share
Authorization: Bearer <access_token>
```

### 4.5 获取分享统计
```http
GET /notes/{noteId}/share/stats
Authorization: Bearer <access_token>
```

**响应:**
```json
{
  "success": true,
  "data": {
    "totalViews": 123,
    "uniqueVisitors": 89,
    "viewsToday": 15,
    "topReferrers": [
      {"source": "direct", "count": 45},
      {"source": "twitter.com", "count": 23}
    ],
    "viewsByDate": [
      {"date": "2023-12-01", "views": 10},
      {"date": "2023-12-02", "views": 15}
    ]
  }
}
```

## 5. macOS 客户端专用 API

### 5.1 同步Apple Notes数据
```http
POST /client/sync
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "appleNotesData": [
    {
      "appleNotesId": "apple-id-1",
      "appleNotesUuid": "apple-uuid-1",
      "title": "笔记标题",
      "content": {
        "type": "rtf",
        "data": "原始RTF数据"
      },
      "createdAt": "2023-12-01T10:00:00Z",
      "modifiedAt": "2023-12-01T15:30:00Z",
      "attachments": [...]
    }
  ],
  "syncMode": "incremental" // full, incremental
}
```

### 5.2 上传笔记内容
```http
POST /client/notes/upload
Authorization: Bearer <access_token>
Content-Type: multipart/form-data

{
  "noteData": {JSON数据},
  "attachments": [文件1, 文件2, ...]
}
```

### 5.3 获取同步状态
```http
GET /client/sync/status
Authorization: Bearer <access_token>
```

### 5.4 批量操作
```http
POST /client/notes/batch
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "operations": [
    {
      "type": "create",
      "data": {...}
    },
    {
      "type": "update",
      "noteId": "uuid",
      "data": {...}
    },
    {
      "type": "delete",
      "noteId": "uuid"
    }
  ]
}
```

## 6. 文件上传 API

### 6.1 获取上传签名 (S3预签名URL)
```http
POST /upload/signature
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "fileName": "photo.jpg",
  "fileSize": 1024000,
  "mimeType": "image/jpeg",
  "noteId": "note-uuid"
}
```

### 6.2 确认上传完成
```http
POST /upload/confirm
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "uploadId": "upload-uuid",
  "fileKey": "s3-object-key",
  "fileHash": "sha256-hash"
}
```

## 7. 搜索 API

### 7.1 搜索笔记
```http
GET /search?q=搜索关键词&type=notes&page=1&limit=20&filters=public:true,tags:工作
Authorization: Bearer <access_token>
```

### 7.2 搜索建议
```http
GET /search/suggestions?q=搜索前缀
Authorization: Bearer <access_token>
```

## 8. 用户设置 API

### 8.1 获取用户设置
```http
GET /user/settings
Authorization: Bearer <access_token>
```

### 8.2 更新用户设置
```http
PUT /user/settings
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "preferences": {
    "theme": "dark",
    "language": "zh-CN",
    "emailNotifications": true,
    "defaultShareType": "private"
  }
}
```

### 8.3 获取用户统计
```http
GET /user/stats
Authorization: Bearer <access_token>
```

## 9. 错误码定义

```typescript
enum ErrorCodes {
  // 认证相关 (1000-1099)
  INVALID_CREDENTIALS = "AUTH_1001",
  TOKEN_EXPIRED = "AUTH_1002",
  TOKEN_INVALID = "AUTH_1003",
  EMAIL_NOT_VERIFIED = "AUTH_1004",
  
  // 笔记相关 (2000-2099)
  NOTE_NOT_FOUND = "NOTE_2001",
  NOTE_ACCESS_DENIED = "NOTE_2002",
  NOTE_CONTENT_TOO_LARGE = "NOTE_2003",
  
  // 分享相关 (3000-3099)
  SHARE_TOKEN_INVALID = "SHARE_3001",
  SHARE_EXPIRED = "SHARE_3002",
  SHARE_VIEW_LIMIT_EXCEEDED = "SHARE_3003",
  SHARE_PASSWORD_REQUIRED = "SHARE_3004",
  
  // 文件相关 (4000-4099)
  FILE_TOO_LARGE = "FILE_4001",
  FILE_TYPE_NOT_SUPPORTED = "FILE_4002",
  UPLOAD_FAILED = "FILE_4003",
  
  // 系统相关 (5000-5099)
  RATE_LIMIT_EXCEEDED = "SYS_5001",
  MAINTENANCE_MODE = "SYS_5002",
  INTERNAL_ERROR = "SYS_5003"
}
```

这个API设计涵盖了Apple Notes分享系统的所有核心功能，提供了清晰的接口规范和完整的错误处理机制。
