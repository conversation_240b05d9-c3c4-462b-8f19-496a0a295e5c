# Apple Notes 分享系统架构方案

## 1. 系统概述

### 1.1 功能目标
- 提取当前打开的Apple Notes笔记内容
- 将笔记发布到Web平台供他人访问
- 支持权限控制和分享管理
- 提供良好的Web阅读体验

### 1.2 核心组件
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   macOS Client  │───▶│   Backend API   │───▶│    Database     │
│  (笔记提取器)    │    │   (Node.js)     │    │  (PostgreSQL)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Web Frontend  │
                       │   (React/Vue)   │
                       └─────────────────┘
```

## 2. 技术栈选择

### 2.1 前端 (Web)
- **框架**: React 18 + TypeScript
- **状态管理**: Zustand 或 Redux Toolkit
- **UI组件**: Tailwind CSS + Headless UI
- **富文本渲染**: 支持Markdown和富文本格式
- **构建工具**: Vite

### 2.2 后端 (API服务)
- **运行时**: Node.js 18+
- **框架**: Express.js 或 Fastify
- **语言**: TypeScript
- **认证**: JWT + Passport.js
- **文件存储**: AWS S3 或本地存储
- **缓存**: Redis

### 2.3 数据库选择分析

#### SQL数据库 (推荐: PostgreSQL)
**优势:**
- 强一致性，适合用户权限和分享关系
- 成熟的事务支持
- 丰富的查询能力
- JSON字段支持，兼顾灵活性

**适用场景:**
- 用户管理和认证
- 笔记元数据和权限控制
- 分享链接管理

#### NoSQL数据库 (辅助: MongoDB/Redis)
**优势:**
- 灵活的文档结构，适合笔记内容存储
- 水平扩展能力强
- 适合非结构化数据

**建议架构:**
```
PostgreSQL (主数据库)
├── 用户信息
├── 笔记元数据
├── 权限控制
└── 分享设置

MongoDB (内容存储)
├── 笔记富文本内容
├── 附件信息
└── 版本历史

Redis (缓存层)
├── 会话缓存
├── 热点笔记缓存
└── 分享链接缓存
```

### 2.4 macOS客户端
- **语言**: Swift 或 Objective-C
- **框架**: Cocoa
- **权限**: 需要辅助功能权限访问Apple Notes
- **通信**: HTTP API调用

## 3. 数据模型设计

### 3.1 核心实体

```sql
-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    display_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 笔记表
CREATE TABLE notes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    title VARCHAR(500) NOT NULL,
    content_id VARCHAR(255), -- MongoDB文档ID
    apple_notes_id VARCHAR(255), -- Apple Notes原始ID
    is_public BOOLEAN DEFAULT FALSE,
    share_token VARCHAR(255) UNIQUE,
    view_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 分享设置表
CREATE TABLE share_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    note_id UUID REFERENCES notes(id),
    share_type VARCHAR(20) CHECK (share_type IN ('public', 'private', 'password')),
    password_hash VARCHAR(255),
    expires_at TIMESTAMP,
    max_views INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 3.2 MongoDB文档结构

```javascript
// 笔记内容文档
{
  _id: ObjectId,
  noteId: "uuid",
  content: {
    type: "rich_text", // 或 "markdown"
    data: "...", // 富文本内容
    attachments: [
      {
        type: "image",
        url: "s3://bucket/path",
        filename: "image.jpg",
        size: 1024000
      }
    ]
  },
  version: 1,
  createdAt: Date,
  updatedAt: Date
}
```

## 4. API接口设计

### 4.1 认证相关
```
POST /api/auth/register     # 用户注册
POST /api/auth/login        # 用户登录
POST /api/auth/refresh      # 刷新Token
POST /api/auth/logout       # 用户登出
```

### 4.2 笔记管理
```
POST /api/notes             # 创建/上传笔记
GET  /api/notes             # 获取用户笔记列表
GET  /api/notes/:id         # 获取特定笔记
PUT  /api/notes/:id         # 更新笔记
DELETE /api/notes/:id       # 删除笔记
```

### 4.3 分享功能
```
POST /api/notes/:id/share   # 创建分享链接
GET  /api/share/:token      # 通过分享链接访问笔记
PUT  /api/notes/:id/share   # 更新分享设置
DELETE /api/notes/:id/share # 取消分享
```

### 4.4 macOS客户端专用
```
POST /api/client/sync       # 同步Apple Notes数据
POST /api/client/upload     # 上传笔记内容
GET  /api/client/status     # 获取同步状态
```

## 5. 安全考虑

### 5.1 认证与授权
- JWT Token认证
- 刷新Token机制
- 基于角色的访问控制(RBAC)

### 5.2 数据安全
- 密码使用bcrypt加密
- 敏感数据传输使用HTTPS
- 分享链接使用随机Token
- 支持密码保护的分享

### 5.3 隐私保护
- 默认私有笔记
- 用户可控的分享权限
- 分享链接过期机制
- 访问日志记录

## 6. 部署架构

### 6.1 开发环境
```
Docker Compose:
├── PostgreSQL
├── MongoDB
├── Redis
├── Backend API
└── Frontend Dev Server
```

### 6.2 生产环境
```
云服务架构:
├── Load Balancer (Nginx)
├── Backend API (多实例)
├── Database Cluster
├── CDN (静态资源)
└── Object Storage (附件)
```

## 7. 开发阶段规划

### Phase 1: 核心功能
- [ ] 后端API基础架构
- [ ] 用户认证系统
- [ ] 基础笔记CRUD
- [ ] macOS客户端集成

### Phase 2: 分享功能
- [ ] 分享链接生成
- [ ] 权限控制
- [ ] Web前端展示

### Phase 3: 增强功能
- [ ] 富文本渲染优化
- [ ] 附件支持
- [ ] 搜索功能
- [ ] 统计分析

## 8. 技术挑战与解决方案

### 8.1 Apple Notes数据提取
- 使用AppleScript或Accessibility API
- 处理富文本格式转换
- 附件文件的提取和上传

### 8.2 富文本渲染
- 统一的富文本格式标准
- 跨平台渲染一致性
- 附件的Web展示

### 8.3 性能优化
- 笔记内容的分页加载
- 图片的懒加载和压缩
- CDN加速静态资源
