{"name": "applenotes-frontend", "version": "1.0.0", "description": "Apple Notes Sharing Platform Frontend", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "zustand": "^4.4.7", "@tanstack/react-query": "^5.17.0", "axios": "^1.6.5", "clsx": "^2.1.0", "lucide-react": "^0.312.0", "react-hot-toast": "^2.4.1", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "date-fns": "^3.2.0", "js-cookie": "^3.0.5"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/js-cookie": "^3.0.6", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.4.0", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.1.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.1"}, "engines": {"node": ">=18.0.0"}}