import { Link } from 'react-router-dom'
import { 
  FileText, 
  Share2, 
  Shield, 
  Zap, 
  Users, 
  Download,
  ArrowRight,
  Check
} from 'lucide-react'
import Button from '@/components/ui/Button'

const HomePage = () => {
  const features = [
    {
      icon: <FileText className="w-6 h-6" />,
      title: '无缝同步',
      description: '直接从 Apple Notes 提取内容，保持原有格式和附件'
    },
    {
      icon: <Share2 className="w-6 h-6" />,
      title: '灵活分享',
      description: '支持公开链接、密码保护、访问限制等多种分享方式'
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: '安全可靠',
      description: '端到端加密，确保你的笔记内容安全私密'
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: '快速便捷',
      description: '一键分享，实时同步，让知识传播更高效'
    }
  ]

  const plans = [
    {
      name: '免费版',
      price: '¥0',
      period: '永久免费',
      features: [
        '最多 10 个笔记',
        '基础分享功能',
        '1GB 存储空间',
        '社区支持'
      ],
      popular: false
    },
    {
      name: '专业版',
      price: '¥29',
      period: '每月',
      features: [
        '无限笔记数量',
        '高级分享选项',
        '10GB 存储空间',
        '优先技术支持',
        '自定义域名',
        '访问统计'
      ],
      popular: true
    },
    {
      name: '团队版',
      price: '¥99',
      period: '每月',
      features: [
        '团队协作功能',
        '无限存储空间',
        '企业级安全',
        '专属客户经理',
        'API 访问',
        '高级分析'
      ],
      popular: false
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-50 to-primary-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              让你的 <span className="text-primary-600">Apple Notes</span>
              <br />
              触达更多人
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              轻松将你的 Apple Notes 笔记分享到网络上，支持富文本、附件，
              提供灵活的权限控制，让知识传播更简单。
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/register">
                <Button variant="primary" size="lg" className="w-full sm:w-auto">
                  免费开始使用
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
              <Link to="/download">
                <Button variant="outline" size="lg" className="w-full sm:w-auto">
                  <Download className="w-5 h-5 mr-2" />
                  下载客户端
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              为什么选择我们？
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              我们专注于提供最佳的 Apple Notes 分享体验
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="text-center p-6 rounded-lg hover:shadow-medium transition-shadow"
              >
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4 text-primary-600">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How it Works */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              使用步骤
            </h2>
            <p className="text-xl text-gray-600">
              三步即可开始分享你的笔记
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white text-2xl font-bold">
                1
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                下载客户端
              </h3>
              <p className="text-gray-600">
                下载并安装 macOS 客户端，连接你的 Apple Notes
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white text-2xl font-bold">
                2
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                选择笔记
              </h3>
              <p className="text-gray-600">
                在 Apple Notes 中打开要分享的笔记，一键同步到平台
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white text-2xl font-bold">
                3
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                分享链接
              </h3>
              <p className="text-gray-600">
                设置权限，生成分享链接，让更多人看到你的内容
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              选择适合你的方案
            </h2>
            <p className="text-xl text-gray-600">
              从个人使用到团队协作，我们都有合适的方案
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {plans.map((plan, index) => (
              <div
                key={index}
                className={`relative rounded-lg border-2 p-8 ${
                  plan.popular
                    ? 'border-primary-500 shadow-large'
                    : 'border-gray-200 shadow-soft'
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-primary-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                      最受欢迎
                    </span>
                  </div>
                )}

                <div className="text-center">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    {plan.name}
                  </h3>
                  <div className="mb-4">
                    <span className="text-4xl font-bold text-gray-900">
                      {plan.price}
                    </span>
                    <span className="text-gray-600 ml-2">
                      {plan.period}
                    </span>
                  </div>
                </div>

                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center">
                      <Check className="w-5 h-5 text-success-500 mr-3 flex-shrink-0" />
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button
                  variant={plan.popular ? 'primary' : 'outline'}
                  className="w-full"
                >
                  {plan.price === '¥0' ? '免费使用' : '立即订阅'}
                </Button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            准备开始了吗？
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            加入数千名用户，开始分享你的知识和想法
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/register">
              <Button variant="secondary" size="lg" className="w-full sm:w-auto">
                免费注册
              </Button>
            </Link>
            <Link to="/contact">
              <Button variant="outline" size="lg" className="w-full sm:w-auto text-white border-white hover:bg-white hover:text-primary-600">
                联系销售
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}

export default HomePage
