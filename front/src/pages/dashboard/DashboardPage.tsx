import { useQuery } from '@tanstack/react-query'
import { 
  FileText, 
  Share2, 
  Eye, 
  TrendingUp,
  Plus,
  Calendar,
  Users
} from 'lucide-react'
import { Link } from 'react-router-dom'
import { userApi, notesApi } from '@/api/notes'
import { useAuthStore } from '@/store/authStore'
import Button from '@/components/ui/Button'

const DashboardPage = () => {
  const { user } = useAuthStore()

  // 获取用户统计数据
  const { data: userStats } = useQuery({
    queryKey: ['user-stats'],
    queryFn: () => userApi.getUserStats().then(res => res.data)
  })

  // 获取最近的笔记
  const { data: recentNotes } = useQuery({
    queryKey: ['recent-notes'],
    queryFn: () => notesApi.getNotes({ 
      limit: 5, 
      sort: 'updated_at', 
      order: 'desc' 
    }).then(res => res.data.notes)
  })

  const stats = [
    {
      name: '总笔记数',
      value: userStats?.totalNotes || 0,
      icon: <FileText className="w-6 h-6" />,
      color: 'bg-blue-500'
    },
    {
      name: '公开笔记',
      value: userStats?.publicNotes || 0,
      icon: <Share2 className="w-6 h-6" />,
      color: 'bg-green-500'
    },
    {
      name: '总浏览量',
      value: userStats?.totalViews || 0,
      icon: <Eye className="w-6 h-6" />,
      color: 'bg-purple-500'
    },
    {
      name: '分享次数',
      value: userStats?.totalShares || 0,
      icon: <TrendingUp className="w-6 h-6" />,
      color: 'bg-orange-500'
    }
  ]

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* 欢迎区域 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          欢迎回来，{user?.displayName}！
        </h1>
        <p className="mt-2 text-gray-600">
          这是你的笔记分享概览
        </p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => (
          <div key={index} className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className={`p-3 rounded-lg ${stat.color} text-white`}>
                  {stat.icon}
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    {stat.name}
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stat.value.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 快速操作 */}
        <div className="lg:col-span-1">
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900">
                快速操作
              </h3>
            </div>
            <div className="card-body space-y-4">
              <Link to="/dashboard/notes/new">
                <Button variant="primary" className="w-full">
                  <Plus className="w-4 h-4 mr-2" />
                  新建笔记
                </Button>
              </Link>
              
              <Link to="/dashboard/notes">
                <Button variant="outline" className="w-full">
                  <FileText className="w-4 h-4 mr-2" />
                  管理笔记
                </Button>
              </Link>
              
              <Link to="/dashboard/settings">
                <Button variant="outline" className="w-full">
                  <Users className="w-4 h-4 mr-2" />
                  账户设置
                </Button>
              </Link>
            </div>
          </div>

          {/* 存储使用情况 */}
          <div className="card mt-6">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900">
                存储使用情况
              </h3>
            </div>
            <div className="card-body">
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">已使用</span>
                  <span className="font-medium">
                    {((userStats?.storageUsed || 0) / 1024 / 1024).toFixed(1)} MB
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-primary-600 h-2 rounded-full" 
                    style={{ 
                      width: `${Math.min(((userStats?.storageUsed || 0) / (1024 * 1024 * 1024)) * 100, 100)}%` 
                    }}
                  />
                </div>
                <div className="flex justify-between text-xs text-gray-500">
                  <span>0 GB</span>
                  <span>1 GB</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 最近的笔记 */}
        <div className="lg:col-span-2">
          <div className="card">
            <div className="card-header flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-900">
                最近更新的笔记
              </h3>
              <Link 
                to="/dashboard/notes"
                className="text-sm text-primary-600 hover:text-primary-700"
              >
                查看全部
              </Link>
            </div>
            <div className="card-body">
              {recentNotes && recentNotes.length > 0 ? (
                <div className="space-y-4">
                  {recentNotes.map((note) => (
                    <div 
                      key={note.id}
                      className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex-1 min-w-0">
                        <Link 
                          to={`/dashboard/notes/${note.id}`}
                          className="block"
                        >
                          <h4 className="text-sm font-medium text-gray-900 truncate">
                            {note.title}
                          </h4>
                          <p className="text-sm text-gray-500 truncate mt-1">
                            {note.contentPreview}
                          </p>
                          <div className="flex items-center mt-2 space-x-4 text-xs text-gray-400">
                            <span className="flex items-center">
                              <Calendar className="w-3 h-3 mr-1" />
                              {new Date(note.updatedAt).toLocaleDateString()}
                            </span>
                            <span className="flex items-center">
                              <Eye className="w-3 h-3 mr-1" />
                              {note.viewCount}
                            </span>
                            {note.isPublic && (
                              <span className="badge badge-success">
                                公开
                              </span>
                            )}
                          </div>
                        </Link>
                      </div>
                      
                      <div className="ml-4 flex items-center space-x-2">
                        {note.shareToken && (
                          <Link 
                            to={`/share/${note.shareToken}`}
                            target="_blank"
                            className="text-primary-600 hover:text-primary-700"
                          >
                            <Share2 className="w-4 h-4" />
                          </Link>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    还没有笔记
                  </h4>
                  <p className="text-gray-500 mb-4">
                    开始创建你的第一个笔记吧
                  </p>
                  <Link to="/dashboard/notes/new">
                    <Button variant="primary">
                      <Plus className="w-4 h-4 mr-2" />
                      新建笔记
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardPage
