import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Link } from 'react-router-dom'
import { 
  Plus, 
  Search, 
  Filter, 
  FileText, 
  Share2, 
  Eye,
  Calendar,
  MoreVertical
} from 'lucide-react'
import { notesApi } from '@/api/notes'
import { NotesQuery, Note } from '@/types'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'

const NotesPage = () => {
  const [query, setQuery] = useState<NotesQuery>({
    page: 1,
    limit: 20,
    sort: 'updated_at',
    order: 'desc'
  })
  const [searchTerm, setSearchTerm] = useState('')

  // 获取笔记列表
  const { data: notesData, isLoading } = useQuery({
    queryKey: ['notes', query],
    queryFn: () => notesApi.getNotes(query).then(res => res.data)
  })

  const handleSearch = (value: string) => {
    setSearchTerm(value)
    setQuery(prev => ({ ...prev, search: value, page: 1 }))
  }

  const handleStatusFilter = (status: string) => {
    setQuery(prev => ({ 
      ...prev, 
      status: status === 'all' ? undefined : status as any,
      page: 1 
    }))
  }

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-12 bg-gray-200 rounded"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* 页面标题和操作 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">我的笔记</h1>
          <p className="mt-2 text-gray-600">
            管理和分享你的笔记
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Link to="/dashboard/notes/new">
            <Button variant="primary">
              <Plus className="w-4 h-4 mr-2" />
              新建笔记
            </Button>
          </Link>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="搜索笔记..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 w-full"
            />
          </div>
        </div>
        
        <div className="flex gap-2">
          <select
            value={query.status || 'all'}
            onChange={(e) => handleStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="all">全部状态</option>
            <option value="draft">草稿</option>
            <option value="published">已发布</option>
            <option value="archived">已归档</option>
          </select>
          
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            筛选
          </Button>
        </div>
      </div>

      {/* 笔记列表 */}
      {notesData?.notes && notesData.notes.length > 0 ? (
        <div className="space-y-4">
          {notesData.notes.map((note: Note) => (
            <div
              key={note.id}
              className="card hover:shadow-medium transition-shadow"
            >
              <div className="card-body">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <Link 
                      to={`/dashboard/notes/${note.id}`}
                      className="block group"
                    >
                      <h3 className="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors truncate">
                        {note.title}
                      </h3>
                      <p className="text-gray-600 mt-1 text-ellipsis-2">
                        {note.contentPreview}
                      </p>
                    </Link>
                    
                    {/* 标签 */}
                    {note.tags && note.tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mt-3">
                        {note.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="badge badge-gray"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}
                    
                    {/* 元信息 */}
                    <div className="flex items-center mt-4 space-x-6 text-sm text-gray-500">
                      <span className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        {new Date(note.updatedAt).toLocaleDateString()}
                      </span>
                      <span className="flex items-center">
                        <Eye className="w-4 h-4 mr-1" />
                        {note.viewCount} 次浏览
                      </span>
                      <span className="flex items-center">
                        <FileText className="w-4 h-4 mr-1" />
                        {note.wordCount} 字
                      </span>
                    </div>
                  </div>
                  
                  {/* 状态和操作 */}
                  <div className="ml-4 flex items-center space-x-3">
                    {/* 状态标签 */}
                    <span className={`badge ${
                      note.status === 'published' ? 'badge-success' :
                      note.status === 'draft' ? 'badge-warning' :
                      'badge-gray'
                    }`}>
                      {note.status === 'published' ? '已发布' :
                       note.status === 'draft' ? '草稿' : '已归档'}
                    </span>
                    
                    {/* 公开状态 */}
                    {note.isPublic && (
                      <span className="badge badge-primary">
                        公开
                      </span>
                    )}
                    
                    {/* 分享按钮 */}
                    {note.shareToken && (
                      <Link 
                        to={`/share/${note.shareToken}`}
                        target="_blank"
                        className="text-primary-600 hover:text-primary-700"
                        title="查看分享页面"
                      >
                        <Share2 className="w-4 h-4" />
                      </Link>
                    )}
                    
                    {/* 更多操作 */}
                    <button className="text-gray-400 hover:text-gray-600">
                      <MoreVertical className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchTerm ? '没有找到匹配的笔记' : '还没有笔记'}
          </h3>
          <p className="text-gray-500 mb-6">
            {searchTerm ? '尝试使用不同的关键词搜索' : '开始创建你的第一个笔记吧'}
          </p>
          <Link to="/dashboard/notes/new">
            <Button variant="primary">
              <Plus className="w-4 h-4 mr-2" />
              新建笔记
            </Button>
          </Link>
        </div>
      )}

      {/* 分页 */}
      {notesData?.meta?.pagination && notesData.meta.pagination.totalPages > 1 && (
        <div className="flex justify-center mt-8">
          <nav className="flex space-x-2">
            {/* 分页组件 - 这里可以创建一个独立的分页组件 */}
            <Button
              variant="outline"
              size="sm"
              disabled={!notesData.meta.pagination.hasPrev}
              onClick={() => setQuery(prev => ({ ...prev, page: prev.page! - 1 }))}
            >
              上一页
            </Button>
            <span className="px-4 py-2 text-sm text-gray-700">
              第 {notesData.meta.pagination.page} 页，共 {notesData.meta.pagination.totalPages} 页
            </span>
            <Button
              variant="outline"
              size="sm"
              disabled={!notesData.meta.pagination.hasNext}
              onClick={() => setQuery(prev => ({ ...prev, page: prev.page! + 1 }))}
            >
              下一页
            </Button>
          </nav>
        </div>
      )}
    </div>
  )
}

export default NotesPage
