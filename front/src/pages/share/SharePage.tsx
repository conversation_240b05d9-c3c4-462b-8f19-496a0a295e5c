import { useParams } from 'react-router-dom'

const SharePage = () => {
  const { shareToken } = useParams<{ shareToken: string }>()

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          分享页面
        </h1>
        <p className="text-gray-600">
          分享令牌: {shareToken}
        </p>
        <p className="text-sm text-gray-500 mt-2">
          此页面正在开发中...
        </p>
      </div>
    </div>
  )
}

export default SharePage
