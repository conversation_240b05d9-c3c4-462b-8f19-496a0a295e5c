import { Link } from 'react-router-dom'
import { Home, ArrowLeft } from 'lucide-react'
import Button from '@/components/ui/Button'

const NotFoundPage = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="text-center">
        <div className="mx-auto max-w-md">
          {/* 404 图标 */}
          <div className="mx-auto flex items-center justify-center h-32 w-32 rounded-full bg-primary-100 mb-8">
            <span className="text-6xl font-bold text-primary-600">404</span>
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            页面未找到
          </h1>
          
          <p className="text-lg text-gray-600 mb-8">
            抱歉，你访问的页面不存在或已被移动。
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant="primary"
              onClick={() => window.history.back()}
              className="flex items-center justify-center"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回上页
            </Button>
            
            <Link to="/">
              <Button variant="outline" className="flex items-center justify-center w-full sm:w-auto">
                <Home className="w-4 h-4 mr-2" />
                回到首页
              </Button>
            </Link>
          </div>
          
          <div className="mt-8 text-sm text-gray-500">
            <p>如果你认为这是一个错误，请</p>
            <Link 
              to="/contact" 
              className="text-primary-600 hover:text-primary-500 font-medium"
            >
              联系我们
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

export default NotFoundPage
