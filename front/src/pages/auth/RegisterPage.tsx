import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Eye, EyeOff } from 'lucide-react'
import { useAuthStore } from '@/store/authStore'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'

const registerSchema = z.object({
  displayName: z
    .string()
    .min(1, '请输入用户名')
    .min(2, '用户名至少需要2个字符')
    .max(50, '用户名不能超过50个字符'),
  email: z
    .string()
    .min(1, '请输入邮箱地址')
    .email('请输入有效的邮箱地址'),
  password: z
    .string()
    .min(1, '请输入密码')
    .min(8, '密码至少需要8位字符')
    .regex(/[A-Z]/, '密码必须包含大写字母')
    .regex(/[a-z]/, '密码必须包含小写字母')
    .regex(/\d/, '密码必须包含数字'),
  confirmPassword: z
    .string()
    .min(1, '请确认密码'),
  acceptTerms: z
    .boolean()
    .refine(val => val === true, '请同意服务条款和隐私政策')
}).refine(data => data.password === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword']
})

type RegisterFormData = z.infer<typeof registerSchema>

const RegisterPage = () => {
  const navigate = useNavigate()
  const { register: registerUser } = useAuthStore()
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema)
  })

  const onSubmit = async (data: RegisterFormData) => {
    try {
      setIsLoading(true)
      await registerUser({
        email: data.email,
        password: data.password,
        displayName: data.displayName,
        acceptTerms: data.acceptTerms
      })
      
      // 注册成功后重定向到仪表板
      navigate('/dashboard', { replace: true })
    } catch (error: any) {
      // 处理特定的注册错误
      if (error.response?.status === 409) {
        setError('email', { message: '该邮箱地址已被注册' })
      } else if (error.response?.data?.error?.details) {
        // 处理验证错误
        const details = error.response.data.error.details
        details.forEach((detail: any) => {
          if (detail.path) {
            setError(detail.path[0] as keyof RegisterFormData, {
              message: detail.message
            })
          }
        })
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div>
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900">
          创建账户
        </h2>
        <p className="mt-2 text-gray-600">
          开始你的笔记分享之旅
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div>
          <Input
            label="用户名"
            type="text"
            placeholder="请输入用户名"
            error={errors.displayName?.message}
            {...register('displayName')}
          />
        </div>

        <div>
          <Input
            label="邮箱地址"
            type="email"
            placeholder="请输入邮箱地址"
            error={errors.email?.message}
            {...register('email')}
          />
        </div>

        <div>
          <div className="relative">
            <Input
              label="密码"
              type={showPassword ? 'text' : 'password'}
              placeholder="请输入密码"
              error={errors.password?.message}
              {...register('password')}
            />
            <button
              type="button"
              className="absolute right-3 top-9 text-gray-400 hover:text-gray-600"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeOff className="w-5 h-5" />
              ) : (
                <Eye className="w-5 h-5" />
              )}
            </button>
          </div>
          <div className="mt-1 text-xs text-gray-500">
            密码必须包含大小写字母和数字，至少8位字符
          </div>
        </div>

        <div>
          <div className="relative">
            <Input
              label="确认密码"
              type={showConfirmPassword ? 'text' : 'password'}
              placeholder="请再次输入密码"
              error={errors.confirmPassword?.message}
              {...register('confirmPassword')}
            />
            <button
              type="button"
              className="absolute right-3 top-9 text-gray-400 hover:text-gray-600"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? (
                <EyeOff className="w-5 h-5" />
              ) : (
                <Eye className="w-5 h-5" />
              )}
            </button>
          </div>
        </div>

        <div>
          <div className="flex items-start">
            <input
              id="accept-terms"
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded mt-1"
              {...register('acceptTerms')}
            />
            <label htmlFor="accept-terms" className="ml-2 block text-sm text-gray-900">
              我已阅读并同意
              <Link
                to="/terms"
                className="text-primary-600 hover:text-primary-500 mx-1"
                target="_blank"
              >
                服务条款
              </Link>
              和
              <Link
                to="/privacy"
                className="text-primary-600 hover:text-primary-500 mx-1"
                target="_blank"
              >
                隐私政策
              </Link>
            </label>
          </div>
          {errors.acceptTerms && (
            <p className="mt-1 text-sm text-error-600">
              {errors.acceptTerms.message}
            </p>
          )}
        </div>

        <div>
          <Button
            type="submit"
            variant="primary"
            loading={isLoading}
            className="w-full"
          >
            创建账户
          </Button>
        </div>

        <div className="text-center">
          <span className="text-gray-600">已有账户？</span>
          <Link
            to="/login"
            className="ml-1 font-medium text-primary-600 hover:text-primary-500"
          >
            立即登录
          </Link>
        </div>
      </form>

      {/* 社交注册 (可选) */}
      <div className="mt-6">
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">或者</span>
          </div>
        </div>

        <div className="mt-6 grid grid-cols-2 gap-3">
          <Button
            variant="outline"
            className="w-full"
            onClick={() => {
              // TODO: 实现 Google 注册
            }}
          >
            <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="currentColor"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="currentColor"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="currentColor"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            Google
          </Button>

          <Button
            variant="outline"
            className="w-full"
            onClick={() => {
              // TODO: 实现 Apple 注册
            }}
          >
            <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"
              />
            </svg>
            Apple
          </Button>
        </div>
      </div>
    </div>
  )
}

export default RegisterPage
