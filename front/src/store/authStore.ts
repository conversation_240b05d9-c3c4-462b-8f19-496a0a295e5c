import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User, AuthTokens, LoginCredentials, RegisterData } from '@/types'
import { authApi } from '@/api/auth'
import toast from 'react-hot-toast'

interface AuthState {
  user: User | null
  tokens: AuthTokens | null
  isAuthenticated: boolean
  isLoading: boolean
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>
  register: (data: RegisterData) => Promise<void>
  logout: () => Promise<void>
  refreshToken: () => Promise<void>
  updateUser: (user: Partial<User>) => void
  initializeAuth: () => Promise<void>
  clearAuth: () => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      tokens: null,
      isAuthenticated: false,
      isLoading: true,

      login: async (credentials: LoginCredentials) => {
        try {
          set({ isLoading: true })
          
          const response = await authApi.login(credentials)
          const { user, tokens } = response.data
          
          set({
            user,
            tokens,
            isAuthenticated: true,
            isLoading: false
          })
          
          toast.success('登录成功')
        } catch (error: any) {
          set({ isLoading: false })
          const message = error.response?.data?.error?.message || '登录失败'
          toast.error(message)
          throw error
        }
      },

      register: async (data: RegisterData) => {
        try {
          set({ isLoading: true })
          
          const response = await authApi.register(data)
          const { user, tokens } = response.data
          
          set({
            user,
            tokens,
            isAuthenticated: true,
            isLoading: false
          })
          
          toast.success('注册成功')
        } catch (error: any) {
          set({ isLoading: false })
          const message = error.response?.data?.error?.message || '注册失败'
          toast.error(message)
          throw error
        }
      },

      logout: async () => {
        try {
          const { tokens } = get()
          if (tokens) {
            await authApi.logout()
          }
        } catch (error) {
          console.error('Logout error:', error)
        } finally {
          set({
            user: null,
            tokens: null,
            isAuthenticated: false,
            isLoading: false
          })
          toast.success('已退出登录')
        }
      },

      refreshToken: async () => {
        try {
          const { tokens } = get()
          if (!tokens?.refreshToken) {
            throw new Error('No refresh token available')
          }

          const response = await authApi.refreshToken(tokens.refreshToken)
          const newTokens = response.data

          set(state => ({
            tokens: newTokens,
            isAuthenticated: !!state.user
          }))
        } catch (error) {
          console.error('Token refresh failed:', error)
          get().clearAuth()
          throw error
        }
      },

      updateUser: (userData: Partial<User>) => {
        set(state => ({
          user: state.user ? { ...state.user, ...userData } : null
        }))
      },

      initializeAuth: async () => {
        try {
          const { tokens, user } = get()
          
          if (!tokens || !user) {
            set({ isLoading: false })
            return
          }

          // 检查 token 是否过期
          const tokenExpiry = new Date(Date.now() + tokens.expiresIn * 1000)
          const now = new Date()
          
          if (tokenExpiry <= now) {
            // Token 过期，尝试刷新
            try {
              await get().refreshToken()
            } catch (error) {
              get().clearAuth()
              return
            }
          }

          // 验证用户信息
          try {
            const response = await authApi.getCurrentUser()
            set({
              user: response.data,
              isAuthenticated: true,
              isLoading: false
            })
          } catch (error) {
            console.error('Failed to get current user:', error)
            get().clearAuth()
          }
        } catch (error) {
          console.error('Auth initialization failed:', error)
          get().clearAuth()
        }
      },

      clearAuth: () => {
        set({
          user: null,
          tokens: null,
          isAuthenticated: false,
          isLoading: false
        })
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        tokens: state.tokens,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)
