import { useState } from 'react'
import { clsx } from 'clsx'
import { User } from 'lucide-react'

interface AvatarProps {
  src?: string
  alt?: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  fallback?: string
}

const Avatar = ({ 
  src, 
  alt, 
  size = 'md', 
  className,
  fallback 
}: AvatarProps) => {
  const [imageError, setImageError] = useState(false)

  const sizeClasses = {
    xs: 'w-6 h-6',
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  }

  const iconSizes = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-8 h-8'
  }

  const textSizes = {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  }

  const classes = clsx(
    'rounded-full bg-gray-200 flex items-center justify-center overflow-hidden',
    sizeClasses[size],
    className
  )

  // 如果有图片且没有错误，显示图片
  if (src && !imageError) {
    return (
      <div className={classes}>
        <img
          src={src}
          alt={alt}
          className="w-full h-full object-cover"
          onError={() => setImageError(true)}
        />
      </div>
    )
  }

  // 如果有 fallback 文字，显示首字母
  if (fallback) {
    const initials = fallback
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)

    return (
      <div className={clsx(classes, 'bg-primary-100 text-primary-700')}>
        <span className={clsx('font-medium', textSizes[size])}>
          {initials}
        </span>
      </div>
    )
  }

  // 默认显示用户图标
  return (
    <div className={clsx(classes, 'text-gray-400')}>
      <User className={iconSizes[size]} />
    </div>
  )
}

export default Avatar
