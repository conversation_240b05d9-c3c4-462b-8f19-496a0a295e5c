import { forwardRef } from 'react'
import { clsx } from 'clsx'
import { InputProps } from '@/types'

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      error,
      placeholder,
      type = 'text',
      value,
      onChange,
      disabled = false,
      required = false,
      className,
      ...props
    },
    ref
  ) => {
    const inputClasses = clsx(
      'input',
      {
        'input-error': error
      },
      className
    )

    return (
      <div className="w-full">
        {label && (
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {label}
            {required && <span className="text-error-500 ml-1">*</span>}
          </label>
        )}
        
        <input
          ref={ref}
          type={type}
          value={value}
          onChange={(e) => onChange?.(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          className={inputClasses}
          {...props}
        />
        
        {error && (
          <p className="mt-1 text-sm text-error-600">{error}</p>
        )}
      </div>
    )
  }
)

Input.displayName = 'Input'

export default Input
