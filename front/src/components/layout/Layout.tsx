import { Outlet } from 'react-router-dom'
import Header from './Header'
import Footer from './Footer'
import { useAuthStore } from '@/store/authStore'

const Layout = () => {
  const { isAuthenticated } = useAuthStore()

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Header />
      
      <main className="flex-1">
        <Outlet />
      </main>
      
      {!isAuthenticated && <Footer />}
    </div>
  )
}

export default Layout
