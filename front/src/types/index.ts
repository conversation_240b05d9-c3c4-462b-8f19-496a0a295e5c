// 用户相关类型
export interface User {
  id: string
  email: string
  displayName: string
  avatarUrl?: string
  emailVerified: boolean
  createdAt: string
  updatedAt: string
  preferences?: UserPreferences
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system'
  language: string
  emailNotifications: boolean
  defaultShareType: ShareType
}

// 认证相关类型
export interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresIn: number
}

export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterData {
  email: string
  password: string
  displayName: string
  acceptTerms: boolean
}

// 笔记相关类型
export interface Note {
  id: string
  userId: string
  title: string
  contentPreview: string
  status: NoteStatus
  isPublic: boolean
  shareToken?: string
  tags: string[]
  wordCount: number
  hasAttachments: boolean
  viewCount: number
  createdAt: string
  updatedAt: string
  publishedAt?: string
  shareSettings?: ShareSettings
}

export interface NoteContent {
  html: string
  plainText: string
  wordCount: number
}

export interface NoteDetail extends Note {
  content: NoteContent
  attachments: Attachment[]
  author?: {
    displayName: string
    avatarUrl?: string
  }
}

export type NoteStatus = 'draft' | 'published' | 'archived'

// 分享相关类型
export interface ShareSettings {
  shareType: ShareType
  shareToken: string
  password?: string
  expiresAt?: string
  maxViews?: number
  currentViews: number
  allowDownload: boolean
  createdAt: string
}

export type ShareType = 'private' | 'public' | 'password' | 'link'

export interface ShareInfo {
  allowDownload: boolean
  expiresAt?: string
  remainingViews?: number
}

// 附件相关类型
export interface Attachment {
  id: string
  type: AttachmentType
  originalName: string
  url: string
  thumbnailUrl?: string
  size: number
  mimeType: string
  metadata?: AttachmentMetadata
}

export type AttachmentType = 'image' | 'file' | 'audio' | 'video'

export interface AttachmentMetadata {
  width?: number
  height?: number
  duration?: number
  [key: string]: any
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  meta?: {
    pagination?: PaginationMeta
    timestamp: string
  }
}

export interface ApiError {
  success: false
  error: {
    code: string
    message: string
    details?: any
  }
  meta: {
    timestamp: string
    requestId: string
  }
}

export interface PaginationMeta {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

// 查询参数类型
export interface NotesQuery {
  page?: number
  limit?: number
  status?: NoteStatus
  search?: string
  tags?: string[]
  sort?: 'created_at' | 'updated_at' | 'title' | 'view_count'
  order?: 'asc' | 'desc'
}

export interface ShareQuery {
  password?: string
}

// 表单类型
export interface CreateNoteForm {
  title: string
  content: {
    type: 'html' | 'markdown' | 'rtf'
    data: string
  }
  tags: string[]
  isPublic: boolean
}

export interface UpdateNoteForm extends Partial<CreateNoteForm> {
  id: string
}

export interface CreateShareForm {
  shareType: ShareType
  password?: string
  expiresAt?: string
  maxViews?: number
  allowDownload: boolean
}

export interface UpdateShareForm extends Partial<CreateShareForm> {}

// 统计类型
export interface UserStats {
  totalNotes: number
  publicNotes: number
  totalViews: number
  totalShares: number
  storageUsed: number
}

export interface ShareStats {
  totalViews: number
  uniqueVisitors: number
  viewsToday: number
  topReferrers: Array<{
    source: string
    count: number
  }>
  viewsByDate: Array<{
    date: string
    views: number
  }>
}

// 组件 Props 类型
export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  loading?: boolean
  disabled?: boolean
  children: React.ReactNode
  onClick?: () => void
  type?: 'button' | 'submit' | 'reset'
  className?: string
}

export interface InputProps {
  label?: string
  error?: string
  placeholder?: string
  type?: string
  value?: string
  onChange?: (value: string) => void
  disabled?: boolean
  required?: boolean
  className?: string
}

export interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  children: React.ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
}

// 主题类型
export interface Theme {
  name: string
  colors: {
    primary: string
    secondary: string
    background: string
    surface: string
    text: string
    textSecondary: string
    border: string
    error: string
    warning: string
    success: string
  }
}

// 路由类型
export interface RouteConfig {
  path: string
  element: React.ComponentType
  protected?: boolean
  title?: string
  description?: string
}

// 错误类型
export interface AppError extends Error {
  code?: string
  status?: number
  details?: any
}

// 环境变量类型
export interface ImportMetaEnv {
  readonly VITE_API_URL: string
  readonly VITE_APP_NAME: string
  readonly VITE_APP_VERSION: string
  readonly VITE_SENTRY_DSN?: string
  readonly VITE_GOOGLE_ANALYTICS_ID?: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
