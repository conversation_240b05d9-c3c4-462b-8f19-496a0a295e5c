import { api } from './client'
import { User, AuthTokens, LoginCredentials, RegisterData, ApiResponse } from '@/types'

export const authApi = {
  // 用户登录
  login: (credentials: LoginCredentials) => {
    return api.post<{ user: User; tokens: AuthTokens }>('/auth/login', credentials)
  },

  // 用户注册
  register: (data: RegisterData) => {
    return api.post<{ user: User; tokens: AuthTokens }>('/auth/register', data)
  },

  // 刷新 Token
  refreshToken: (refreshToken: string) => {
    return api.post<AuthTokens>('/auth/refresh', { refreshToken })
  },

  // 用户登出
  logout: () => {
    return api.post('/auth/logout')
  },

  // 获取当前用户信息
  getCurrentUser: () => {
    return api.get<User>('/auth/me')
  },

  // 更新用户信息
  updateProfile: (data: Partial<User>) => {
    return api.put<User>('/auth/profile', data)
  },

  // 修改密码
  changePassword: (data: { currentPassword: string; newPassword: string }) => {
    return api.post('/auth/change-password', data)
  },

  // 发送邮箱验证
  sendEmailVerification: () => {
    return api.post('/auth/send-verification')
  },

  // 验证邮箱
  verifyEmail: (token: string) => {
    return api.post('/auth/verify-email', { token })
  },

  // 发送密码重置邮件
  sendPasswordReset: (email: string) => {
    return api.post('/auth/forgot-password', { email })
  },

  // 重置密码
  resetPassword: (data: { token: string; newPassword: string }) => {
    return api.post('/auth/reset-password', data)
  }
}
