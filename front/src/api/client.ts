import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ApiResponse, ApiError } from '@/types'
import { useAuthStore } from '@/store/authStore'
import toast from 'react-hot-toast'

// 创建 axios 实例
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001/api',
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  })

  // 请求拦截器
  client.interceptors.request.use(
    (config) => {
      // 添加认证 token
      const { tokens } = useAuthStore.getState()
      if (tokens?.accessToken) {
        config.headers.Authorization = `Bearer ${tokens.accessToken}`
      }

      // 添加请求 ID
      config.headers['X-Request-ID'] = generateRequestId()

      // 添加客户端信息
      config.headers['X-Client-Version'] = import.meta.env.VITE_APP_VERSION || '1.0.0'
      config.headers['X-Platform'] = 'web'

      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  client.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      return response
    },
    async (error) => {
      const originalRequest = error.config

      // 处理 401 未授权错误
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true

        try {
          const { refreshToken } = useAuthStore.getState()
          if (refreshToken) {
            await refreshToken()
            // 重新发送原始请求
            return client(originalRequest)
          }
        } catch (refreshError) {
          // 刷新失败，清除认证状态
          useAuthStore.getState().clearAuth()
          window.location.href = '/login'
          return Promise.reject(refreshError)
        }
      }

      // 处理网络错误
      if (!error.response) {
        toast.error('网络连接失败，请检查网络设置')
        return Promise.reject(new Error('Network Error'))
      }

      // 处理服务器错误
      const apiError: ApiError = error.response.data
      
      // 根据错误类型显示不同的提示
      switch (error.response.status) {
        case 400:
          // 客户端错误，通常是验证失败
          break
        case 403:
          toast.error('没有权限执行此操作')
          break
        case 404:
          toast.error('请求的资源不存在')
          break
        case 429:
          toast.error('请求过于频繁，请稍后再试')
          break
        case 500:
          toast.error('服务器内部错误，请稍后再试')
          break
        default:
          if (apiError?.error?.message) {
            toast.error(apiError.error.message)
          } else {
            toast.error('请求失败，请稍后再试')
          }
      }

      return Promise.reject(error)
    }
  )

  return client
}

// 生成请求 ID
const generateRequestId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

// 创建 API 客户端实例
export const apiClient = createApiClient()

// 通用 API 请求方法
export class ApiClient {
  private client: AxiosInstance

  constructor() {
    this.client = apiClient
  }

  async get<T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.client.get(url, config)
  }

  async post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.client.post(url, data, config)
  }

  async put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.client.put(url, data, config)
  }

  async patch<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.client.patch(url, data, config)
  }

  async delete<T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.client.delete(url, config)
  }

  // 文件上传
  async upload<T = any>(
    url: string,
    file: File,
    onProgress?: (progress: number) => void,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<ApiResponse<T>>> {
    const formData = new FormData()
    formData.append('file', file)

    return this.client.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          )
          onProgress(progress)
        }
      },
    })
  }

  // 下载文件
  async download(
    url: string,
    filename?: string,
    config?: AxiosRequestConfig
  ): Promise<void> {
    const response = await this.client.get(url, {
      ...config,
      responseType: 'blob',
    })

    // 创建下载链接
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  }
}

// 导出默认实例
export const api = new ApiClient()
