import { api } from './client'
import { 
  Note, 
  NoteDetail, 
  NotesQuery, 
  CreateNoteForm, 
  UpdateNoteForm,
  CreateShareForm,
  UpdateShareForm,
  ShareSettings,
  ShareStats,
  UserStats,
  PaginationMeta
} from '@/types'

export const notesApi = {
  // 获取笔记列表
  getNotes: (query: NotesQuery = {}) => {
    const params = new URLSearchParams()
    
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          params.append(key, value.join(','))
        } else {
          params.append(key, String(value))
        }
      }
    })

    return api.get<{
      notes: Note[]
      meta: { pagination: PaginationMeta }
    }>(`/notes?${params.toString()}`)
  },

  // 获取单个笔记详情
  getNote: (noteId: string) => {
    return api.get<{ note: NoteDetail }>(`/notes/${noteId}`)
  },

  // 创建笔记
  createNote: (data: CreateNoteForm) => {
    return api.post<{ note: Note }>('/notes', data)
  },

  // 更新笔记
  updateNote: (noteId: string, data: Partial<UpdateNoteForm>) => {
    return api.put<{ note: Note }>(`/notes/${noteId}`, data)
  },

  // 删除笔记
  deleteNote: (noteId: string) => {
    return api.delete(`/notes/${noteId}`)
  },

  // 批量删除笔记
  deleteNotes: (noteIds: string[]) => {
    return api.post('/notes/batch-delete', { noteIds })
  },

  // 复制笔记
  duplicateNote: (noteId: string) => {
    return api.post<{ note: Note }>(`/notes/${noteId}/duplicate`)
  },

  // 导出笔记
  exportNote: (noteId: string, format: 'html' | 'markdown' | 'pdf') => {
    return api.download(`/notes/${noteId}/export?format=${format}`)
  },

  // 搜索笔记
  searchNotes: (query: string, filters?: Partial<NotesQuery>) => {
    const params = new URLSearchParams({ q: query })
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            params.append(key, value.join(','))
          } else {
            params.append(key, String(value))
          }
        }
      })
    }

    return api.get<{
      notes: Note[]
      meta: { pagination: PaginationMeta }
    }>(`/search?${params.toString()}`)
  },

  // 获取搜索建议
  getSearchSuggestions: (query: string) => {
    return api.get<{ suggestions: string[] }>(`/search/suggestions?q=${encodeURIComponent(query)}`)
  }
}

export const shareApi = {
  // 创建分享链接
  createShare: (noteId: string, data: CreateShareForm) => {
    return api.post<{
      shareUrl: string
      shareToken: string
      shareSettings: ShareSettings
    }>(`/notes/${noteId}/share`, data)
  },

  // 更新分享设置
  updateShare: (noteId: string, data: UpdateShareForm) => {
    return api.put<{ shareSettings: ShareSettings }>(`/notes/${noteId}/share`, data)
  },

  // 取消分享
  deleteShare: (noteId: string) => {
    return api.delete(`/notes/${noteId}/share`)
  },

  // 通过分享链接获取笔记
  getSharedNote: (shareToken: string, password?: string) => {
    const data = password ? { password } : undefined
    return api.post<{
      note: NoteDetail
      shareInfo: {
        allowDownload: boolean
        expiresAt?: string
        remainingViews?: number
      }
    }>(`/share/${shareToken}`, data)
  },

  // 获取分享统计
  getShareStats: (noteId: string) => {
    return api.get<ShareStats>(`/notes/${noteId}/share/stats`)
  }
}

export const userApi = {
  // 获取用户统计
  getUserStats: () => {
    return api.get<UserStats>('/user/stats')
  },

  // 获取用户设置
  getUserSettings: () => {
    return api.get('/user/settings')
  },

  // 更新用户设置
  updateUserSettings: (settings: any) => {
    return api.put('/user/settings', { preferences: settings })
  }
}

export const uploadApi = {
  // 获取上传签名
  getUploadSignature: (data: {
    fileName: string
    fileSize: number
    mimeType: string
    noteId?: string
  }) => {
    return api.post<{
      uploadUrl: string
      uploadId: string
      fields: Record<string, string>
    }>('/upload/signature', data)
  },

  // 确认上传完成
  confirmUpload: (data: {
    uploadId: string
    fileKey: string
    fileHash: string
  }) => {
    return api.post<{
      attachment: {
        id: string
        url: string
        thumbnailUrl?: string
      }
    }>('/upload/confirm', data)
  },

  // 直接上传文件 (小文件)
  uploadFile: (file: File, noteId?: string, onProgress?: (progress: number) => void) => {
    const formData = new FormData()
    formData.append('file', file)
    if (noteId) {
      formData.append('noteId', noteId)
    }

    return api.upload('/upload', file, onProgress)
  }
}
