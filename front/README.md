# Apple Notes Share - Frontend

这是 Apple Notes 分享平台的前端应用，基于 React 18 + TypeScript + Vite 构建。

## 🚀 快速开始

### 环境要求

- Node.js 18+
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 环境配置

复制环境变量文件并配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置 API 地址等参数。

### 启动开发服务器

```bash
npm run dev
```

应用将在 http://localhost:3000 启动。

## 📦 构建

### 开发构建

```bash
npm run build
```

### 生产构建

```bash
npm run build
```

构建文件将输出到 `dist` 目录。

## 🧪 测试

```bash
# 运行测试
npm run test

# 运行测试 UI
npm run test:ui
```

## 📝 代码规范

### 代码检查

```bash
# 检查代码规范
npm run lint

# 自动修复
npm run lint:fix
```

### 代码格式化

```bash
npm run format
```

### 类型检查

```bash
npm run type-check
```

## 🏗️ 项目结构

```
src/
├── api/              # API 接口
├── components/       # 可复用组件
│   ├── auth/        # 认证相关组件
│   ├── layout/      # 布局组件
│   └── ui/          # 基础 UI 组件
├── hooks/           # 自定义 Hooks
├── pages/           # 页面组件
├── store/           # 状态管理
├── types/           # TypeScript 类型定义
├── utils/           # 工具函数
└── assets/          # 静态资源
```

## 🔧 技术栈

- **框架**: React 18
- **语言**: TypeScript
- **构建工具**: Vite
- **路由**: React Router v6
- **状态管理**: Zustand
- **数据获取**: TanStack Query
- **HTTP 客户端**: Axios
- **样式**: Tailwind CSS
- **表单**: React Hook Form + Zod
- **图标**: Lucide React
- **通知**: React Hot Toast

## 🎨 设计系统

项目使用 Tailwind CSS 构建了一套完整的设计系统：

- **颜色**: 主色调为蓝色，支持深色模式
- **字体**: Inter 字体
- **组件**: 统一的按钮、输入框、卡片等组件
- **响应式**: 移动端优先的响应式设计

## 📱 功能特性

- ✅ 用户认证（登录/注册）
- ✅ 响应式设计
- ✅ 路由守卫
- ✅ 错误处理
- ✅ 加载状态
- ✅ 表单验证
- 🚧 笔记管理
- 🚧 分享功能
- 🚧 文件上传
- 🚧 搜索功能

## 🔐 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `VITE_API_URL` | API 服务地址 | `http://localhost:3001/api` |
| `VITE_APP_NAME` | 应用名称 | `Apple Notes Share` |
| `VITE_APP_VERSION` | 应用版本 | `1.0.0` |

## 📄 许可证

MIT License
